# ONNX车辆重识别推理工具

这是一个基于ONNX Runtime的车辆重识别推理工具，支持使用osnet_ain_x0_75.onnx模型进行车辆图片相似度计算。

## 功能特性

- 支持ONNX模型推理，兼容CPU和CUDA
- 自动图像预处理（resize、归一化等）
- 支持余弦相似度和欧氏距离两种相似度计算方法
- 完整的错误处理和日志输出
- 规范的代码注释和文档

## 环境要求

```bash
pip install onnxruntime-gpu  # 或 onnxruntime (仅CPU)
pip install torch torchvision
pip install pillow
pip install numpy
```

## 文件说明

- `onnx_inference.py` - 主要的ONNX推理代码
- `test_onnx_inference.py` - 测试脚本
- `osnet_ain_x0_75.onnx` - ONNX模型文件（需要存在）

## 使用方法

### 1. 命令行使用

```bash
# 基本用法
python onnx_inference.py --img1 path/to/image1.jpg --img2 path/to/image2.jpg

# 指定模型路径
python onnx_inference.py --model osnet_ain_x0_75.onnx --img1 img1.jpg --img2 img2.jpg

# 使用欧氏距离
python onnx_inference.py --img1 img1.jpg --img2 img2.jpg --metric euclidean

# 强制使用CPU
python onnx_inference.py --img1 img1.jpg --img2 img2.jpg --cpu

# 自定义输入尺寸
python onnx_inference.py --img1 img1.jpg --img2 img2.jpg --size 256 256
```

### 2. 代码中使用

```python
from onnx_inference import ONNXReIDInference

# 创建推理对象
inference = ONNXReIDInference(
    model_path="osnet_ain_x0_75.onnx",
    input_size=(256, 256),
    use_cuda=True
)

# 比较两张图片
similarity = inference.compare_images(
    "image1.jpg", 
    "image2.jpg", 
    metric='cosine'
)

print(f"相似度: {similarity:.4f}")
```

### 3. 运行测试

```bash
# 运行测试脚本
python test_onnx_inference.py
```

## 参数说明

### 命令行参数

- `--model`: ONNX模型文件路径（默认: osnet_ain_x0_75.onnx）
- `--img1`: 第一张图片路径（必需）
- `--img2`: 第二张图片路径（必需）
- `--metric`: 相似度计算方法，cosine或euclidean（默认: cosine）
- `--size`: 输入图片尺寸 [height, width]（默认: [256, 256]）
- `--cpu`: 强制使用CPU推理

### 相似度方法

1. **余弦相似度 (cosine)**
   - 范围: [0, 1]
   - 值越大越相似
   - 推荐阈值: >0.8 高度相似, >0.6 中等相似

2. **欧氏距离 (euclidean)**
   - 范围: [0, +∞)
   - 值越小越相似
   - 推荐阈值: <0.5 高度相似, <1.0 中等相似

## 模型信息

- **模型**: osnet_ain_x0_75
- **输入尺寸**: 256×256×3
- **特征维度**: 384
- **归一化参数**: 
  - mean: [0.4209, 0.4206, 0.4267]
  - std: [0.1855, 0.1843, 0.1838]

## 性能说明

- GPU推理时间: ~5-10ms/张
- CPU推理时间: ~50-100ms/张
- 内存占用: ~200MB

## 错误处理

代码包含完整的错误处理机制：

- 模型文件不存在检查
- 图片文件不存在检查
- CUDA可用性自动检测
- 图像预处理错误处理
- 推理过程异常捕获

## 示例输出

```
ONNX模型加载成功: osnet_ain_x0_75.onnx
输入尺寸: (256, 256)
使用设备: CUDA
正在比较图片:
图片1: image1.jpg
图片2: image2.jpg
相似度方法: cosine
--------------------------------------------------
特征提取时间: 8.45ms
特征提取时间: 7.92ms
--------------------------------------------------
余弦相似度: 0.8234
结果: 高度相似

最终结果: 0.8234
```

## 注意事项

1. 确保ONNX模型文件存在且路径正确
2. 输入图片支持常见格式（jpg, png, bmp等）
3. 图片会自动resize到指定尺寸，无需预处理
4. 建议使用GPU加速以获得更好的性能
5. 相似度阈值需要根据具体应用场景调整
