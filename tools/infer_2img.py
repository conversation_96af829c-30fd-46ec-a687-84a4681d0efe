"""Visualizes CNN activation maps to see where the CNN focuses on to extract features.

Reference:
    - <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Paying more attention to attention: Improving the
      performance of convolutional neural networks via attention transfer. ICLR, 2017
    - <PERSON> et al. Omni-Scale Feature Learning for Person Re-Identification. ICCV, 2019.
"""
import numpy as np
import os.path as osp
import argparse
import cv2
from PIL import Image
import torch
import torchvision.transforms as T
from torch.nn import functional as F

import torchreid
from torchreid.utils import (
    check_isfile, mkdir_if_missing, load_pretrained_weights
)

IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]
GRID_SPACING = 10

def euclidean_distance(qf, gf):
    m = qf.shape[0]
    n = gf.shape[0]
    dist_mat = torch.pow(qf, 2).sum(dim=1, keepdim=True).expand(m, n) + \
               torch.pow(gf, 2).sum(dim=1, keepdim=True).expand(n, m).t()
    dist_mat.addmm_(1, -2, qf, gf.t())
    return dist_mat.detach().cpu().numpy()

def cosine_similarity(qf, gf):
    epsilon = 0.00001
    dist_mat = qf.mm(gf.t())
    qf_norm = torch.norm(qf, p=2, dim=1, keepdim=True)  # mx1
    gf_norm = torch.norm(gf, p=2, dim=1, keepdim=True)  # nx1
    qg_normdot = qf_norm.mm(gf_norm.t())

    dist_mat = dist_mat.mul(1 / qg_normdot).cpu().numpy()
    dist_mat = np.clip(dist_mat, -1 + epsilon, 1 - epsilon)
    dist_mat = np.arccos(dist_mat)
    return dist_mat

@torch.no_grad()
def infer_2img(
    model,
    test_loader,
    save_dir,
    width,
    height,
    use_gpu,
    img1_path,
    img2_path,
    img_mean=None,
    img_std=None
):
    if img_mean is None or img_std is None:
        # use imagenet mean and std
        img_mean = IMAGENET_MEAN
        img_std = IMAGENET_STD

    val_transforms = T.Compose([
        T.Resize([128, 128]),
        T.ToTensor(),
        T.Normalize(mean=IMAGENET_MEAN, std=IMAGENET_STD)
    ])

    model.eval()

    img1 = Image.open(img1_path).convert('RGB')
    img1 = val_transforms(img1)
    img1 = img1.unsqueeze(0).cuda()

    img2 = Image.open(img2_path).convert('RGB')
    img2 = val_transforms(img2)
    img2 = img2.unsqueeze(0).cuda()

    feat1 = model(img1)  # torch.Size([1, 512])
    feat2 = model(img2)  # torch.Size([1, 512])

    feat1 = torch.nn.functional.normalize(feat1, dim=1, p=2).cpu()
    feat2 = torch.nn.functional.normalize(feat2, dim=1, p=2).cpu()

    dist = euclidean_distance(feat1, feat2)
    print(dist)  

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--root', default="/home/<USER>/project/ReID/datasets", type=str)
    parser.add_argument('-d', '--dataset', type=str, default='veri')
    parser.add_argument('-m', '--model', type=str, default='osnet_x1_0')
    parser.add_argument('--weights', default="log/osnet_x1_0_VeRi_softmax_cosinelr/model/model.pth.tar-250",type=str)
    parser.add_argument('--save-dir', type=str, default='log')
    parser.add_argument('--height', type=int, default=128)
    parser.add_argument('--width', type=int, default=128)
    args = parser.parse_args()

    use_gpu = torch.cuda.is_available()

    datamanager = torchreid.data.ImageDataManager(
        root=args.root,
        sources=args.dataset,
        height=args.height,
        width=args.width,
        batch_size_train=100,
        batch_size_test=100,
        transforms=None,
        train_sampler='SequentialSampler'
    )
    test_loader = datamanager.test_loader

    model = torchreid.models.build_model(
        name=args.model,
        num_classes=datamanager.num_train_pids,
        use_gpu=use_gpu
    )

    if use_gpu:
        model = model.cuda()

    if args.weights and check_isfile(args.weights):
        load_pretrained_weights(model, args.weights)

    img1_path = "datas/cars/0129_c017_00027580_0.jpg"
    img2_path = "datas/cars/0129_c015_00026705_1.jpg"
    infer_2img(
        model, test_loader, args.save_dir, args.width, args.height, use_gpu, img1_path, img2_path
    )


if __name__ == '__main__':
    main()
