#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
 * @Name: onnx_inference.py
 * @Description: 使用ONNX模型进行车辆重识别推理，计算两张图片的相似度
 *
 * @Input
 * - model_path: ONNX模型文件路径
 * - img1_path: 第一张图片路径
 * - img2_path: 第二张图片路径
 *
 * @Output
 * - similarity: 两张图片的相似度分数
 *
 * @Edit History
 * Date: 2024-12-19
 * Time: 10:00:00
 * Author: JiaTao
 * Content: 创建ONNX推理代码，支持osnet_ain_x0_75模型
"""

import numpy as np
import onnxruntime as ort
from PIL import Image
import torch
import torchvision.transforms as T
import argparse
import os
import time


class ONNXReIDInference:
    """
     * @Name: ONNXReIDInference
     * @Description: ONNX车辆重识别推理类
     *
     * @Input
     * - model_path: ONNX模型文件路径
     * - input_size: 输入图片尺寸，默认(256, 256)
     * - use_cuda: 是否使用CUDA加速
     *
     * @Output
     * - 初始化的推理对象
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 10:00:00
     * Author: JiaTao
     * Content: 创建推理类
    """
    
    def __init__(self, model_path, input_size=(256, 256), use_cuda=True):
        self.model_path = model_path
        self.input_size = input_size
        
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"ONNX模型文件不存在: {model_path}")
        
        # 初始化ONNX运行时会话
        self._init_session(use_cuda)
        
        # 初始化图像预处理
        self._init_transforms()
        
        print(f"ONNX模型加载成功: {model_path}")
        print(f"输入尺寸: {input_size}")
        print(f"使用设备: {'CUDA' if self.use_cuda else 'CPU'}")
    
    def _init_session(self, use_cuda):
        """
         * @Name: _init_session
         * @Description: 初始化ONNX运行时会话
         *
         * @Input
         * - use_cuda: 是否使用CUDA加速
         *
         * @Output
         * - 无
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 10:00:00
         * Author: JiaTao
         * Content: 初始化ONNX会话
        """
        try:
            if use_cuda and ort.get_device() == 'GPU':
                providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
                self.session = ort.InferenceSession(self.model_path, providers=providers)
                self.use_cuda = True
                print("使用CUDA加速")
            else:
                providers = ['CPUExecutionProvider']
                self.session = ort.InferenceSession(self.model_path, providers=providers)
                self.use_cuda = False
                print("使用CPU推理")
        except Exception as e:
            print(f"CUDA初始化失败，回退到CPU: {e}")
            providers = ['CPUExecutionProvider']
            self.session = ort.InferenceSession(self.model_path, providers=providers)
            self.use_cuda = False
    
    def _init_transforms(self):
        """
         * @Name: _init_transforms
         * @Description: 初始化图像预处理变换
         *
         * @Input
         * - 无
         *
         * @Output
         * - 无
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 10:00:00
         * Author: JiaTao
         * Content: 初始化图像预处理
        """
        # 使用VeRi数据集的归一化参数
        self.transforms = T.Compose([
            T.Resize(self.input_size),
            T.ToTensor(),
            T.Normalize(mean=[0.4209, 0.4206, 0.4267], std=[0.1855, 0.1843, 0.1838])
        ])
    
    def preprocess_image(self, img_path):
        """
         * @Name: preprocess_image
         * @Description: 图像预处理
         *
         * @Input
         * - img_path: 图片文件路径
         *
         * @Output
         * - img_np: 预处理后的numpy数组
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 10:00:00
         * Author: JiaTao
         * Content: 图像预处理函数
        """
        try:
            # 加载图像并转换为RGB
            img = Image.open(img_path).convert('RGB')
            
            # 应用预处理变换
            img_tensor = self.transforms(img)
            
            # 添加批次维度并转换为numpy数组
            img_np = img_tensor.unsqueeze(0).numpy()
            
            return img_np
        except Exception as e:
            raise RuntimeError(f"图像预处理失败 {img_path}: {e}")
    
    def extract_feature(self, img_path):
        """
         * @Name: extract_feature
         * @Description: 提取图像特征向量
         *
         * @Input
         * - img_path: 图片文件路径
         *
         * @Output
         * - feature: 归一化后的特征向量
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 10:00:00
         * Author: JiaTao
         * Content: 特征提取函数
        """
        # 预处理图像
        img_np = self.preprocess_image(img_path)
        
        # ONNX推理
        start_time = time.time()
        outputs = self.session.run(["output"], {"images": img_np})
        inference_time = time.time() - start_time
        
        # 获取特征向量
        feature = outputs[0]  # shape: (1, feature_dim)
        
        # 转换为PyTorch张量并进行L2归一化
        feature_tensor = torch.from_numpy(feature)
        feature_normalized = torch.nn.functional.normalize(feature_tensor, dim=1, p=2)
        
        print(f"特征提取时间: {inference_time*1000:.2f}ms")

        return feature_normalized

    def compute_similarity(self, feature1, feature2, metric='cosine'):
        """
         * @Name: compute_similarity
         * @Description: 计算两个特征向量的相似度
         *
         * @Input
         * - feature1: 第一个特征向量
         * - feature2: 第二个特征向量
         * - metric: 相似度计算方法，'cosine'或'euclidean'
         *
         * @Output
         * - similarity: 相似度分数
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 10:00:00
         * Author: JiaTao
         * Content: 相似度计算函数
        """
        if metric == 'cosine':
            # 余弦相似度 (值越大越相似，范围[0,1])
            similarity = torch.mm(feature1, feature2.t()).item()
            return similarity
        elif metric == 'euclidean':
            # 欧氏距离 (值越小越相似)
            distance = torch.dist(feature1, feature2, p=2).item()
            return distance
        else:
            raise ValueError(f"不支持的相似度计算方法: {metric}")

    def compare_images(self, img1_path, img2_path, metric='cosine'):
        """
         * @Name: compare_images
         * @Description: 比较两张图片的相似度
         *
         * @Input
         * - img1_path: 第一张图片路径
         * - img2_path: 第二张图片路径
         * - metric: 相似度计算方法
         *
         * @Output
         * - similarity: 相似度分数
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 10:00:00
         * Author: JiaTao
         * Content: 图片比较函数
        """
        print(f"正在比较图片:")
        print(f"图片1: {img1_path}")
        print(f"图片2: {img2_path}")
        print(f"相似度方法: {metric}")
        print("-" * 50)

        # 提取特征
        feature1 = self.extract_feature(img1_path)
        feature2 = self.extract_feature(img2_path)

        # 计算相似度
        similarity = self.compute_similarity(feature1, feature2, metric)

        print("-" * 50)
        if metric == 'cosine':
            print(f"余弦相似度: {similarity:.4f}")
            if similarity > 0.8:
                print("结果: 高度相似")
            elif similarity > 0.6:
                print("结果: 中等相似")
            else:
                print("结果: 低相似度")
        else:  # euclidean
            print(f"欧氏距离: {similarity:.4f}")
            if similarity < 0.5:
                print("结果: 高度相似")
            elif similarity < 1.0:
                print("结果: 中等相似")
            else:
                print("结果: 低相似度")

        return similarity


def main():
    """
     * @Name: main
     * @Description: 主函数，解析命令行参数并执行推理
     *
     * @Input
     * - 命令行参数
     *
     * @Output
     * - 相似度结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 10:00:00
     * Author: JiaTao
     * Content: 主函数
    """
    parser = argparse.ArgumentParser(description='ONNX车辆重识别推理工具')
    parser.add_argument('--model', type=str, default='osnet_ain_x0_75.onnx',
                        help='ONNX模型文件路径')
    parser.add_argument('--img1', type=str, required=True,
                        help='第一张图片路径')
    parser.add_argument('--img2', type=str, required=True,
                        help='第二张图片路径')
    parser.add_argument('--metric', type=str, default='cosine',
                        choices=['cosine', 'euclidean'],
                        help='相似度计算方法')
    parser.add_argument('--size', type=int, nargs=2, default=[256, 256],
                        help='输入图片尺寸 [height, width]')
    parser.add_argument('--cpu', action='store_true',
                        help='强制使用CPU推理')

    args = parser.parse_args()

    try:
        # 创建推理对象
        inference = ONNXReIDInference(
            model_path=args.model,
            input_size=tuple(args.size),
            use_cuda=not args.cpu
        )

        # 比较图片
        similarity = inference.compare_images(args.img1, args.img2, args.metric)

        print(f"\n最终结果: {similarity:.4f}")

    except Exception as e:
        print(f"推理过程中出现错误: {e}")
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
