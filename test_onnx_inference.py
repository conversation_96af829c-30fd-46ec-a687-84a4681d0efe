#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
 * @Name: test_onnx_inference.py
 * @Description: 测试ONNX推理代码的示例脚本
 *
 * @Input
 * - 无
 *
 * @Output
 * - 测试结果
 *
 * @Edit History
 * Date: 2024-12-19
 * Time: 10:00:00
 * Author: JiaTao
 * Content: 创建测试脚本
"""

import os
from onnx_inference import ONNXReIDInference


def test_onnx_inference():
    """
     * @Name: test_onnx_inference
     * @Description: 测试ONNX推理功能
     *
     * @Input
     * - 无
     *
     * @Output
     * - 测试结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 10:00:00
     * Author: JiaTao
     * Content: 测试函数
    """
    # 模型路径
    model_path = "osnet_ain_x0_75.onnx"
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        print(f"错误: ONNX模型文件不存在: {model_path}")
        print("请确保模型文件在当前目录下")
        return False
    
    # 测试图片路径（使用项目中的示例图片）
    img1_path = "datas/cars/1_1.png"
    img2_path = "datas/cars/1_2.png"
    
    # 检查测试图片是否存在
    if not os.path.exists(img1_path):
        print(f"错误: 测试图片1不存在: {img1_path}")
        return False
    
    if not os.path.exists(img2_path):
        print(f"错误: 测试图片2不存在: {img2_path}")
        return False
    
    try:
        print("=" * 60)
        print("开始测试ONNX推理功能")
        print("=" * 60)
        
        # 创建推理对象
        inference = ONNXReIDInference(
            model_path=model_path,
            input_size=(256, 256),
            use_cuda=True
        )
        
        print("\n测试1: 余弦相似度")
        print("-" * 40)
        similarity_cosine = inference.compare_images(img1_path, img2_path, 'cosine')
        
        print("\n测试2: 欧氏距离")
        print("-" * 40)
        similarity_euclidean = inference.compare_images(img1_path, img2_path, 'euclidean')
        
        print("\n" + "=" * 60)
        print("测试完成")
        print(f"余弦相似度: {similarity_cosine:.4f}")
        print(f"欧氏距离: {similarity_euclidean:.4f}")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        return False


def test_with_same_image():
    """
     * @Name: test_with_same_image
     * @Description: 使用相同图片测试，验证相似度应该很高
     *
     * @Input
     * - 无
     *
     * @Output
     * - 测试结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 10:00:00
     * Author: JiaTao
     * Content: 相同图片测试函数
    """
    model_path = "osnet_ain_x0_75.onnx"
    img_path = "datas/cars/1_1.png"
    
    if not os.path.exists(model_path) or not os.path.exists(img_path):
        print("跳过相同图片测试：文件不存在")
        return False
    
    try:
        print("\n" + "=" * 60)
        print("测试相同图片的相似度（应该接近1.0）")
        print("=" * 60)
        
        inference = ONNXReIDInference(model_path, use_cuda=True)
        similarity = inference.compare_images(img_path, img_path, 'cosine')
        
        print(f"\n相同图片的余弦相似度: {similarity:.6f}")
        
        if similarity > 0.99:
            print("✓ 相同图片测试通过")
            return True
        else:
            print("✗ 相同图片测试失败，相似度应该接近1.0")
            return False
            
    except Exception as e:
        print(f"相同图片测试出错: {e}")
        return False


if __name__ == "__main__":
    print("ONNX车辆重识别推理测试")
    print("请确保以下文件存在：")
    print("1. osnet_ain_x0_75.onnx")
    print("2. datas/cars/1_1.png")
    print("3. datas/cars/1_2.png")
    print()
    
    # 运行基本测试
    success1 = test_onnx_inference()
    
    # 运行相同图片测试
    success2 = test_with_same_image()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
