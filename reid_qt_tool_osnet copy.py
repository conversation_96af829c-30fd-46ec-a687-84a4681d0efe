#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy as np
from PIL import Image
import torch
import torchvision.transforms as T
import sys, os, time
sys.path.append(".")

# import torchreid.models as torchreid_model
from torchreid.utils import (
    load_pretrained_weights
)

from ultralytics import YOLO

from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout,
                            QLabel, QFileDialog, QWidget, QGridLayout, QComboBox, QScrollArea,
                            QProgressBar, QMessageBox)
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt, QThread, pyqtSignal


# 复用原ReID代码中的距离计算函数
def euclidean_distance(qf, gf):
    m = qf.shape[0]
    n = gf.shape[0]
    dist_mat = torch.pow(qf, 2).sum(dim=1, keepdim=True).expand(m, n) + \
               torch.pow(gf, 2).sum(dim=1, keepdim=True).expand(n, m).t()
    dist_mat.addmm_(1, -2, qf, gf.t())
    return dist_mat.detach().cpu().numpy()

def cosine_similarity(qf, gf):
    epsilon = 0.00001
    dist_mat = qf.mm(gf.t())
    qf_norm = torch.norm(qf, p=2, dim=1, keepdim=True)  # mx1
    gf_norm = torch.norm(gf, p=2, dim=1, keepdim=True)  # nx1
    qg_normdot = qf_norm.mm(gf_norm.t())

    dist_mat = dist_mat.mul(1 / qg_normdot).cpu().numpy()
    dist_mat = np.clip(dist_mat, -1 + epsilon, 1 - epsilon)
    dist_mat = np.arccos(dist_mat)
    return dist_mat

class FeatureExtractorWorker(QThread):
    """特征提取线程，避免UI卡顿"""
    progress_updated = pyqtSignal(int)
    finished_with_results = pyqtSignal(object, object, list, float)  # 添加平均时间参数
    error_occurred = pyqtSignal(str)
    
    def __init__(self, model, query_img_path, gallery_dir, similarity_method, top_k=10):
        super().__init__()
        self.model = model
        self.query_img_path = query_img_path
        self.gallery_dir = gallery_dir
        self.similarity_method = similarity_method
        self.top_k = top_k
        self.val_transforms = T.Compose([
            T.Resize([128, 128]),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
    def get_image_files(self, dir_path):
        """获取目录中的所有图片文件"""
        import glob
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
        image_files = []

        for ext in extensions:
            image_files.extend(glob.glob(os.path.join(dir_path, ext)))
            # 递归搜索子目录
            image_files.extend(glob.glob(os.path.join(dir_path, '**', ext), recursive=True))

        return sorted(list(set(image_files)))  # 去重并排序
        
    def extract_feature(self, img_path):
        """提取单张图片特征"""
        try:
            img = Image.open(img_path).convert('RGB')
            img = self.val_transforms(img)
            img = img.unsqueeze(0).cuda()

            with torch.no_grad():
                feat = self.model(img)
                print(feat.shape)
                feat = torch.nn.functional.normalize(feat, dim=1, p=2).cpu()

            return feat
        except Exception as e:
            self.error_occurred.emit(f"处理图片 {img_path} 时出错: {str(e)}")
            return None
    
    def run(self):
        try:
            # 提取查询图片特征
            query_feature = self.extract_feature(self.query_img_path)
            if query_feature is None:
                self.error_occurred.emit("查询图片特征提取失败")
                return
                
            # 获取图库中的所有图片
            gallery_paths = self.get_image_files(self.gallery_dir)
            if not gallery_paths:
                self.error_occurred.emit(f"在目录 {self.gallery_dir} 中没有找到图片")
                return
            
            # 用于计算平均时间的列表
            processing_times = []
            
            # 提取图库图片特征
            gallery_features = []
            for i, path in enumerate(gallery_paths):
                start_time = time.time()
                feat = self.extract_feature(path)
                end_time = time.time()
                
                if feat is not None:
                    gallery_features.append(feat)
                    processing_times.append(end_time - start_time)
                
                # 更新进度
                progress = int((i + 1) / len(gallery_paths) * 100)
                self.progress_updated.emit(progress)
            
            if not gallery_features:
                self.error_occurred.emit("图库图片特征提取失败")
                return
            
            # 计算平均处理时间（忽略前10%的数据）
            if processing_times:
                skip_count = int(len(processing_times) * 0.1)  # 忽略前10%的数据
                avg_time = sum(processing_times[skip_count:]) / len(processing_times[skip_count:])
            else:
                avg_time = 0
                
            # 合并特征向量
            gallery_features_tensor = torch.cat(gallery_features, dim=0)
            
            # 计算相似度
            if self.similarity_method.lower() == "cosine":
                similarity_matrix = cosine_similarity(query_feature, gallery_features_tensor)
            else:  # 默认使用欧氏距离
                similarity_matrix = euclidean_distance(query_feature, gallery_features_tensor)
                
            # 获取前K个最相似的图片
            distances = similarity_matrix[0]  # 只有一张查询图片
            top_indices = np.argsort(distances)[:self.top_k]
            top_gallery_paths = [gallery_paths[i] for i in top_indices]
            top_distances = [distances[i] for i in top_indices]
            
            # 发送结果信号，包含平均处理时间
            self.finished_with_results.emit(
                query_feature, 
                gallery_features_tensor, 
                [(top_gallery_paths[i], top_distances[i]) for i in range(len(top_indices))],
                avg_time
            )
        except Exception as e:
            self.error_occurred.emit(f"处理过程中出错: {str(e)}")

class ReIDApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.query_img_path = None
        self.gallery_dir = None
        self.model = None
        self.init_ui()
        self.init_model()
        
    def init_ui(self):
        self.setWindowTitle("车辆重识别工具")
        self.setGeometry(100, 100, 1200, 800)
        
        # 主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 顶部控制区域
        control_layout = QHBoxLayout()
        
        # 查询图片选择
        query_btn = QPushButton("选择查询图片")
        query_btn.clicked.connect(self.select_query_image)
        control_layout.addWidget(query_btn)
        
        # 图库目录选择
        gallery_btn = QPushButton("选择图库目录")
        gallery_btn.clicked.connect(self.select_gallery_dir)
        control_layout.addWidget(gallery_btn)
        
        # 相似度方法选择
        self.similarity_combo = QComboBox()
        self.similarity_combo.addItems(["euclidean", "cosine"])
        control_layout.addWidget(QLabel("相似度方法:"))
        control_layout.addWidget(self.similarity_combo)
        
        # 开始搜索按钮
        search_btn = QPushButton("开始搜索")
        search_btn.clicked.connect(self.start_search)
        control_layout.addWidget(search_btn)
        
        main_layout.addLayout(control_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 图像显示区域
        display_layout = QHBoxLayout()
        
        # 查询图片显示
        query_layout = QVBoxLayout()
        query_layout.addWidget(QLabel("查询图片:"))
        self.query_img_label = QLabel()
        self.query_img_label.setAlignment(Qt.AlignCenter)
        self.query_img_label.setMinimumSize(300, 300)
        self.query_img_label.setStyleSheet("border: 1px solid black")
        query_layout.addWidget(self.query_img_label)
        display_layout.addLayout(query_layout)
        
        # 结果图片显示
        results_layout = QVBoxLayout()
        results_layout.addWidget(QLabel("搜索结果:"))
        
        # 创建一个滚动区域来容纳结果网格
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        results_container = QWidget()
        self.results_grid = QGridLayout(results_container)
        scroll_area.setWidget(results_container)
        results_layout.addWidget(scroll_area)
        
        display_layout.addLayout(results_layout)
        main_layout.addLayout(display_layout)
        
        # 初始化结果显示网格
        self.result_labels = []
        # 添加表头
        self.results_grid.addWidget(QLabel("图片"), 0, 0)
        self.results_grid.addWidget(QLabel("信息"), 0, 1)
        
        for i in range(10):  # 显示前5个结果
            img_label = QLabel()
            img_label.setAlignment(Qt.AlignCenter)
            img_label.setMinimumSize(200, 200)
            img_label.setStyleSheet("border: 1px solid #cccccc")
            
            # 修改为包含图片名称和距离的信息标签
            info_label = QLabel("图片名称: N/A\n距离: N/A")
            info_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            info_label.setWordWrap(True)
            info_label.setMinimumWidth(200)
            
            # 将图片和信息标签添加到网格
            self.results_grid.addWidget(img_label, i+1, 0)
            self.results_grid.addWidget(info_label, i+1, 1)
            
            self.result_labels.append((img_label, info_label))
        
        print("init ui ok \n")
    
    def init_model(self):
        """初始化模型"""
        try:
            weight_path = "log/im_yolo11n_softmax_256x256_amsgrad_cosine_veri/model/model.pth.tar-250"
                
            if not os.path.exists(weight_path):
                QMessageBox.warning(self, "错误", f"模型权重文件不存在: {weight_path}\n请修改代码中的路径")
                return
            
            # 加载模型
            self.model = YOLO("ultralytics/cfg/models/11/yolo11-reid.yaml").model
            load_pretrained_weights(self.model, weight_path)
            self.model.eval()
            self.model.to('cuda')
            
            QMessageBox.information(self, "成功", "模型加载成功")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"模型加载失败: {str(e)}")
    
    def select_query_image(self):
        """选择查询图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择查询图片", "", "图片文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.query_img_path = file_path
            self.display_image(file_path, self.query_img_label)
    
    def select_gallery_dir(self):
        """选择图库目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择图库目录")
        if dir_path:
            self.gallery_dir = dir_path
    
    def display_image(self, img_path, label, max_size=300):
        """在标签中显示图片"""
        try:
            pixmap = QPixmap(img_path)
            # 等比例缩放图片
            pixmap = pixmap.scaled(
                max_size, max_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )
            label.setPixmap(pixmap)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法显示图片: {str(e)}")
    
    def start_search(self):
        """开始搜索相似图片"""
        if self.model is None:
            QMessageBox.warning(self, "错误", "模型未加载")
            return
            
        if not self.query_img_path:
            QMessageBox.warning(self, "错误", "请先选择查询图片")
            return
            
        if not self.gallery_dir:
            QMessageBox.warning(self, "错误", "请先选择图库目录")
            return
        
        # 清空之前的结果
        for img_label, info_label in self.result_labels:
            img_label.clear()
            info_label.setText("图片名称: N/A\n距离: N/A")
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 创建并启动工作线程
        similarity_method = self.similarity_combo.currentText()
        self.worker = FeatureExtractorWorker(
            self.model, self.query_img_path, self.gallery_dir, similarity_method
        )
        
        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.finished_with_results.connect(self.display_results)
        self.worker.error_occurred.connect(self.show_error)
        
        # 启动线程
        self.worker.start()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def display_results(self, query_feature, gallery_features, results, avg_time):
        """显示搜索结果"""
        for i, (result_path, distance) in enumerate(results):
            if i < len(self.result_labels):
                img_label, info_label = self.result_labels[i]
                
                # 显示图片
                self.display_image(result_path, img_label, max_size=200)
                
                # 提取图片名称并显示信息
                img_name = os.path.basename(result_path)
                info_text = f"图片名称: {img_name}\n距离: {distance:.4f}"
                info_label.setText(info_text)
        
        # 进度条完成
        self.progress_bar.setValue(100)
        
        QMessageBox.information(self, "完成", f"搜索完成\n平均特征提取时间: {avg_time*1000:.2f}ms")
    
    def show_error(self, error_msg):
        """显示错误信息"""
        QMessageBox.critical(self, "错误", error_msg)
        self.progress_bar.setValue(0)

if __name__ == "__main__":
    import os
    os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = "/home/<USER>/anaconda3/envs/ult/lib/python3.10/site-packages/PyQt5/Qt5/plugins"
    # 禁用OpenCV的Qt插件搜索
    os.environ["QT_QPA_PLATFORM"] = "xcb"
    app = QApplication(sys.argv)
    window = ReIDApp()
    window.show()
    sys.exit(app.exec_()) 
