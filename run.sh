PATH_TO_DATA=/home/<USER>/project/ReID/datasets
export https_proxy=http://127.0.0.1:7897 http_proxy=http://127.0.0.1:7897 all_proxy=socks5://127.0.0.1:7897

# python scripts/main.py \
# --config-file configs/im_osnet_x1_0_softmax_256x256_amsgrad_cosine_veri.yaml \
# --transforms random_flip random_erase \
# --root $PATH_TO_DATA \
# model.load_weights log/osnet_x1_0_VeRi_softmax_cosinelr/model/model.pth.tar-50 \
# test.evaluate True


# python scripts/main.py \
# --config-file configs/im_osnet_x1_0_softmax_128x128_amsgrad_cosine_veri.yaml \
# --transforms random_flip random_erase \
# --root $PATH_TO_DATA \
# test.batch_size 10 \
# model.load_weights log/osnet_x1_0_VeRi_softmax_cosinelr/model/model.pth.tar-250 \
# test.evaluate True

python scripts/main.py \
--config-file configs/im_osnet_ain_x0_75_TripletLoss_256x256_amsgrad_cosine_veri.yaml \
--transforms random_flip random_erase \
--root $PATH_TO_DATA \
test.batch_size 10 