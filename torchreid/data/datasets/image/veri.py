from __future__ import division, print_function, absolute_import
import re
import glob
import os.path as osp
import warnings

from ..dataset import ImageDataset


class VeRi(ImageDataset):
    """VeRi.

    Reference:
        <PERSON> et al. Large-scale vehicle re-identification in urban surveillance videos. In: IEEE
        International Conference on Multimedia and Expo. (2016).

    URL: `<https://github.com/JDAI-CV/VeRidataset>`_
    
    Dataset statistics:
        - identities: 776 vehicles.
        - images: 37778 (train) + 1678 (query) + 11579 (gallery).
    """
    _junk_pids = [0, -1]
    dataset_dir = 'VeRi'

    def __init__(self, root='', **kwargs):
        self.root = osp.abspath(osp.expanduser(root))
        self.dataset_dir = osp.join(self.root, self.dataset_dir)

        # 数据目录
        self.train_dir = osp.join(self.dataset_dir, 'image_train')
        self.query_dir = osp.join(self.dataset_dir, 'image_query')
        self.gallery_dir = osp.join(self.dataset_dir, 'image_test')

        required_files = [
            self.dataset_dir, self.train_dir, self.query_dir, self.gallery_dir
        ]
        self.check_before_run(required_files)

        train = self.process_dir(self.train_dir, relabel=True)
        query = self.process_dir(self.query_dir, relabel=False)
        gallery = self.process_dir(self.gallery_dir, relabel=False)

        super(VeRi, self).__init__(train, query, gallery, **kwargs)

    def process_dir(self, dir_path, relabel=False):
        img_paths = glob.glob(osp.join(dir_path, '*.jpg'))
        pattern = re.compile(r'(\d+)_c(\d+)_(\d+)')

        pid_container = set()
        for img_path in img_paths:
            pid, _, _ = map(int, pattern.search(img_path).groups())
            pid_container.add(pid)
        pid2label = {pid: label for label, pid in enumerate(pid_container)}

        data = []
        for img_path in img_paths:
            pid, camid, _ = map(int, pattern.search(img_path).groups())
            if relabel:
                pid = pid2label[pid]
            # 摄像头ID从0开始
            camid -= 1
            data.append((img_path, pid, camid))

        return data 