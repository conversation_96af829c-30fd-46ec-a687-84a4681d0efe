#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import glob
from collections import Counter
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='分析YOLO格式标签文件')
    parser.add_argument(
        '--labels-dir', 
        type=str, 
        default='/home/<USER>/Traffic/SpillCutoutTrainDataset/labels',
        help='标签文件目录路径'
    )
    parser.add_argument(
        '--class-file', 
        type=str, 
        default=None,
        help='类别映射文件路径（可选）'
    )
    return parser.parse_args()

def load_class_names(class_file):
    """加载类别名称映射文件"""
    if not class_file or not os.path.exists(class_file):
        return None
    
    try:
        with open(class_file, 'r', encoding='utf-8') as f:
            class_names = [line.strip() for line in f.readlines()]
        return class_names
    except Exception as e:
        logger.error(f"加载类别映射文件失败: {e}")
        return None

def get_all_label_files(labels_dir):
    """递归获取目录及其所有子目录中的所有.txt文件"""
    if not os.path.exists(labels_dir):
        logger.error(f"目录不存在: {labels_dir}")
        return []
    
    label_files = []
    for root, _, files in os.walk(labels_dir):
        for file in files:
            if file.endswith('.txt'):
                label_files.append(os.path.join(root, file))
    
    return label_files

def analyze_yolo_labels(labels_dir, class_names=None):
    """分析YOLO格式标签文件"""
    # 获取所有txt文件（包括子目录）
    label_files = get_all_label_files(labels_dir)
    if not label_files:
        logger.warning(f"在 {labels_dir} 及其子目录中未找到标签文件")
        return
    
    logger.info(f"开始分析 {len(label_files)} 个标签文件...")
    
    # 统计类别计数
    class_counter = Counter()
    invalid_lines = 0
    empty_files = 0
    processed_files = 0
    
    # 遍历所有标签文件
    for label_file in label_files:
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                lines = f.read().strip().splitlines()
                
                if not lines:
                    empty_files += 1
                    continue
                
                for line in lines:
                    try:
                        parts = line.strip().split()
                        if len(parts) >= 5:  # 确保至少有5个元素
                            class_id = int(parts[0])
                            class_counter[class_id] += 1
                        else:
                            invalid_lines += 1
                    except Exception:
                        invalid_lines += 1
            
            processed_files += 1
            # 每处理1000个文件显示进度
            if processed_files % 1000 == 0:
                logger.info(f"已处理 {processed_files}/{len(label_files)} 个文件...")
                
        except Exception as e:
            logger.error(f"处理文件 {label_file} 时出错: {e}")
    
    # 输出统计结果
    if not class_counter:
        logger.warning("未找到有效的类别数据")
        return
    
    logger.info(f"分析完成! 找到 {len(class_counter)} 个不同的类别")
    logger.info(f"处理文件总数: {len(label_files)}")
    logger.info(f"空文件数: {empty_files}")
    logger.info(f"无效行数: {invalid_lines}")
    
    # 按类别索引排序并输出结果
    print("\n类别统计结果:")
    print("-" * 50)
    print(f"{'类别ID':<10}{'出现次数':<15}{'百分比':<10}{'类别名称':<20}")
    print("-" * 50)
    
    total_labels = sum(class_counter.values())
    
    for class_id, count in sorted(class_counter.items()):
        percentage = (count / total_labels) * 100
        class_name = class_names[class_id] if class_names and class_id < len(class_names) else "未知"
        print(f"{class_id:<10}{count:<15}{percentage:.2f}%{'':<4}{class_name:<20}")
    
    print("-" * 50)
    print(f"总标签数: {total_labels}")

def main():
    """主函数"""
    args = parse_args()
    class_names = load_class_names(args.class_file)
    analyze_yolo_labels(args.labels_dir, class_names)

if __name__ == "__main__":
    main()