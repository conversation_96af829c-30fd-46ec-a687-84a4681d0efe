#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
 * @Name: simple_example.py
 * @Description: 简单的ONNX推理使用示例
 *
 * @Input
 * - 两张图片路径
 *
 * @Output
 * - 相似度分数
 *
 * @Edit History
 * Date: 2024-12-19
 * Time: 10:00:00
 * Author: Jia<PERSON>ao
 * Content: 创建简单示例
"""

from onnx_inference import ONNXReIDInference


def simple_comparison():
    """
     * @Name: simple_comparison
     * @Description: 简单的图片相似度比较示例
     *
     * @Input
     * - 无
     *
     * @Output
     * - 相似度结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 10:00:00
     * Author: JiaTao
     * Content: 简单比较函数
    """
    # 初始化推理器
    inference = ONNXReIDInference(
        model_path="osnet_ain_x0_75.onnx",
        input_size=(256, 256),
        use_cuda=True
    )
    
    # 比较两张图片
    img1 = "datas/cars/1_1.png"
    img2 = "datas/cars/1_2.png"
    
    print(f"比较图片: {img1} vs {img2}")
    
    # 计算余弦相似度
    cosine_sim = inference.compare_images(img1, img2, 'cosine')
    
    # 计算欧氏距离
    euclidean_dist = inference.compare_images(img1, img2, 'euclidean')
    
    print(f"\n结果总结:")
    print(f"余弦相似度: {cosine_sim:.4f}")
    print(f"欧氏距离: {euclidean_dist:.4f}")
    
    return cosine_sim, euclidean_dist


def batch_comparison():
    """
     * @Name: batch_comparison
     * @Description: 批量比较多张图片
     *
     * @Input
     * - 无
     *
     * @Output
     * - 批量比较结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 10:00:00
     * Author: JiaTao
     * Content: 批量比较函数
    """
    # 初始化推理器
    inference = ONNXReIDInference("osnet_ain_x0_75.onnx")
    
    # 图片对列表
    image_pairs = [
        ("datas/cars/1_1.png", "datas/cars/1_2.png"),
        ("datas/cars/2_1.png", "datas/cars/2_2.png"),
        ("datas/cars/3_1.png", "datas/cars/3_2.png"),
    ]
    
    print("批量比较结果:")
    print("-" * 60)
    
    for i, (img1, img2) in enumerate(image_pairs, 1):
        try:
            similarity = inference.compare_images(img1, img2, 'cosine')
            print(f"对比 {i}: {similarity:.4f}")
        except Exception as e:
            print(f"对比 {i}: 错误 - {e}")
    
    print("-" * 60)


if __name__ == "__main__":
    print("ONNX推理简单示例")
    print("=" * 50)
    
    # 运行简单比较
    try:
        simple_comparison()
    except Exception as e:
        print(f"简单比较失败: {e}")
    
    print("\n" + "=" * 50)
    
    # 运行批量比较
    try:
        batch_comparison()
    except Exception as e:
        print(f"批量比较失败: {e}")
