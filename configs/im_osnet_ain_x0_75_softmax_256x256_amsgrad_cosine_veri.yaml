model:
  name: 'osnet_ain_x0_75'
  pretrained: True

data:
  type: 'image'
  sources: ['veri']
  targets: ['veri']
  height: 256
  width: 256
  combineall: False
  transforms: ['random_flip']
  save_dir: 'log/im_osnet_x1_0_softmax_256x256_amsgrad_cosine_veri'
  norm_mean: [0.4209, 0.4206, 0.4267]
  norm_std: [0.1855, 0.1843, 0.1838]

loss:
  name: 'softmax'
  softmax:
    label_smooth: True

train:
  optim: 'amsgrad'
  lr: 0.0015
  max_epoch: 250
  batch_size: 64
  fixbase_epoch: 10
  open_layers: ['classifier']
  lr_scheduler: 'cosine'

test:
  batch_size: 5
  dist_metric: 'euclidean'
  normalize_feature: False
  evaluate: False
  eval_freq: 10
  rerank: False