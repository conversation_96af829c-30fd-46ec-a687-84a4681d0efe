model:
  name: 'osnet_x1_0'
  pretrained: True

data:
  type: 'image'
  sources: ['veri']
  targets: ['veri']
  height: 256
  width: 128
  combineall: False
  transforms: ['random_flip']
  save_dir: 'log/osnet_x1_0_VeRi_softmax_cosinelr'

loss:
  name: 'softmax'
  softmax:
    label_smooth: True

train:
  optim: 'amsgrad'
  lr: 0.0015
  max_epoch: 250
  batch_size: 64
  fixbase_epoch: 10
  open_layers: ['classifier']
  lr_scheduler: 'cosine'

test:
  batch_size: 30
  dist_metric: 'euclidean'
  normalize_feature: False
  evaluate: False
  eval_freq: 10
  rerank: False